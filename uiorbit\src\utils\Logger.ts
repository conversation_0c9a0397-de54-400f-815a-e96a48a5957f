import * as vscode from 'vscode';

/**
 * Log levels enum
 */
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

/**
 * Centralized logging utility for UIOrbit extension
 * Provides different log levels and output channel integration
 */
export class Logger {
  private static outputChannel: vscode.OutputChannel | undefined;
  private static logLevel: LogLevel = LogLevel.INFO;

  /**
   * Initialize the logger with VS Code output channel
   */
  static initialize(): void {
    if (!this.outputChannel) {
      this.outputChannel = vscode.window.createOutputChannel('UIOrbit');
    }

    // Set log level from configuration
    const config = vscode.workspace.getConfiguration('uiorbit');
    const debugMode = config.get<boolean>('debugMode', false);
    this.logLevel = debugMode ? LogLevel.DEBUG : LogLevel.INFO;
  }

  /**
   * Log debug message
   */
  static debug(message: string, ...args: any[]): void {
    this.log(LogLevel.DEBUG, message, ...args);
  }

  /**
   * Log info message
   */
  static info(message: string, ...args: any[]): void {
    this.log(LogLevel.INFO, message, ...args);
  }

  /**
   * Log warning message
   */
  static warn(message: string, ...args: any[]): void {
    this.log(LogLevel.WARN, message, ...args);
  }

  /**
   * Log error message
   */
  static error(message: string, error?: any): void {
    let errorMessage = message;
    
    if (error) {
      if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
        if (error.stack && this.logLevel === LogLevel.DEBUG) {
          errorMessage += `\nStack trace: ${error.stack}`;
        }
      } else {
        errorMessage += `: ${JSON.stringify(error)}`;
      }
    }

    this.log(LogLevel.ERROR, errorMessage);
  }

  /**
   * Log message with specified level
   */
  private static log(level: LogLevel, message: string, ...args: any[]): void {
    // Check if we should log this level
    if (level < this.logLevel) {
      return;
    }

    // Ensure output channel is initialized
    if (!this.outputChannel) {
      this.initialize();
    }

    // Format timestamp
    const timestamp = new Date().toISOString();
    const levelStr = LogLevel[level].padEnd(5);
    
    // Format message
    let formattedMessage = `[${timestamp}] [${levelStr}] ${message}`;
    
    if (args.length > 0) {
      const argsStr = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');
      formattedMessage += ` ${argsStr}`;
    }

    // Output to channel
    this.outputChannel?.appendLine(formattedMessage);

    // Also log to console in debug mode
    if (this.logLevel === LogLevel.DEBUG) {
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage);
          break;
        case LogLevel.INFO:
          console.info(formattedMessage);
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage);
          break;
        case LogLevel.ERROR:
          console.error(formattedMessage);
          break;
      }
    }
  }

  /**
   * Show the output channel
   */
  static show(): void {
    this.outputChannel?.show();
  }

  /**
   * Clear the output channel
   */
  static clear(): void {
    this.outputChannel?.clear();
  }

  /**
   * Set log level
   */
  static setLogLevel(level: LogLevel): void {
    this.logLevel = level;
    this.info(`Log level set to: ${LogLevel[level]}`);
  }

  /**
   * Get current log level
   */
  static getLogLevel(): LogLevel {
    return this.logLevel;
  }

  /**
   * Dispose the logger
   */
  static dispose(): void {
    this.outputChannel?.dispose();
    this.outputChannel = undefined;
  }
}

// Initialize logger when module is loaded
Logger.initialize();
