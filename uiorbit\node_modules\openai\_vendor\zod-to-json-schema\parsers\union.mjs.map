{"version": 3, "file": "union.mjs", "sourceRoot": "", "sources": ["../../../src/_vendor/zod-to-json-schema/parsers/union.ts"], "names": [], "mappings": "OACO,EAAmB,QAAQ,EAAE;AAGpC,MAAM,CAAC,MAAM,iBAAiB,GAAG;IAC/B,SAAS,EAAE,QAAQ;IACnB,SAAS,EAAE,QAAQ;IACnB,SAAS,EAAE,SAAS;IACpB,UAAU,EAAE,SAAS;IACrB,OAAO,EAAE,MAAM;CACP,CAAC;AAmBX,MAAM,UAAU,aAAa,CAC3B,GAAqD,EACrD,IAAU;IAEV,IAAI,IAAI,CAAC,MAAM,KAAK,UAAU;QAAE,OAAO,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;IAE1D,MAAM,OAAO,GACX,GAAG,CAAC,OAAO,YAAY,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC;IAE9E,2GAA2G;IAC3G,IACE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,iBAAiB,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EACvG;QACA,+FAA+F;QAE/F,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,KAA6B,EAAE,CAAC,EAAE,EAAE;YAChE,MAAM,IAAI,GAAG,iBAAiB,CAAC,CAAC,CAAC,IAAI,CAAC,QAAwB,CAAC,CAAC,CAAC,oCAAoC;YACrG,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAClE,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAE;SAC3C,CAAC;KACH;SAAM,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,YAAY,IAAI,CAAC,CAAC,CAAC,WAAW,CAAC,EAAE;QACnF,uBAAuB;QAEvB,MAAM,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAA2B,EAAE,CAA0B,EAAE,EAAE;YACvF,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YACjC,QAAQ,IAAI,EAAE;gBACZ,KAAK,QAAQ,CAAC;gBACd,KAAK,QAAQ,CAAC;gBACd,KAAK,SAAS;oBACZ,OAAO,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC,CAAC;gBACxB,KAAK,QAAQ;oBACX,OAAO,CAAC,GAAG,GAAG,EAAE,SAAkB,CAAC,CAAC;gBACtC,KAAK,QAAQ;oBACX,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI;wBAAE,OAAO,CAAC,GAAG,GAAG,EAAE,MAAe,CAAC,CAAC;gBAC9D,KAAK,QAAQ,CAAC;gBACd,KAAK,WAAW,CAAC;gBACjB,KAAK,UAAU,CAAC;gBAChB;oBACE,OAAO,GAAG,CAAC;aACd;QACH,CAAC,EAAE,EAAE,CAAC,CAAC;QAEP,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;YACnC,6EAA6E;YAE7E,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;YAClE,OAAO;gBACL,IAAI,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAE;gBAC5D,IAAI,EAAE,OAAO,CAAC,MAAM,CAClB,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE;oBACT,OAAO,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACnE,CAAC,EACD,EAAmD,CACpD;aACF,CAAC;SACH;KACF;SAAM,IAAI,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,SAAS,CAAC,EAAE;QAC9D,OAAO;YACL,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,OAAO,CAAC,MAAM,CAClB,CAAC,GAAa,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,EACxF,EAAE,CACH;SACF,CAAC;KACH;IAED,OAAO,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC5B,CAAC;AAED,MAAM,OAAO,GAAG,CACd,GAAqD,EACrD,IAAU,EACwD,EAAE;IACpE,MAAM,KAAK,GAAI,CAAC,GAAG,CAAC,OAAO,YAAY,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAW;SACnG,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CACZ,QAAQ,CAAC,CAAC,CAAC,IAAI,EAAE;QACf,GAAG,IAAI;QACP,WAAW,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC;KACpD,CAAC,CACH;SACA,MAAM,CACL,CAAC,CAAC,EAAwB,EAAE,CAC1B,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CACtF,CAAC;IAEJ,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;AAC9C,CAAC,CAAC"}