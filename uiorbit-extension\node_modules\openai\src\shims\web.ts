// @ts-ignore
import * as types from '../_shims/web-types';
import { setShims } from '../_shims/registry';
import { getRuntime } from '../_shims/web-runtime';
setShims(getRuntime({ manuallyImported: true }));

declare module '../_shims/manual-types' {
  export namespace manual {
    // @ts-ignore
    export type Agent = types.Agent;
    // @ts-ignore
    export import fetch = types.fetch;
    // @ts-ignore
    export type Request = types.Request;
    // @ts-ignore
    export type RequestInfo = types.RequestInfo;
    // @ts-ignore
    export type RequestInit = types.RequestInit;
    // @ts-ignore
    export type Response = types.Response;
    // @ts-ignore
    export type ResponseInit = types.ResponseInit;
    // @ts-ignore
    export type ResponseType = types.ResponseType;
    // @ts-ignore
    export type BodyInit = types.BodyInit;
    // @ts-ignore
    export type Headers = types.Headers;
    // @ts-ignore
    export type HeadersInit = types.HeadersInit;
    // @ts-ignore
    export type BlobPropertyBag = types.BlobPropertyBag;
    // @ts-ignore
    export type FilePropertyBag = types.FilePropertyBag;
    // @ts-ignore
    export type FileFromPathOptions = types.FileFromPathOptions;
    // @ts-ignore
    export import FormData = types.FormData;
    // @ts-ignore
    export import File = types.File;
    // @ts-ignore
    export import Blob = types.Blob;
    // @ts-ignore
    export type Readable = types.Readable;
    // @ts-ignore
    export type FsReadStream = types.FsReadStream;
    // @ts-ignore
    export import ReadableStream = types.ReadableStream;
  }
}
